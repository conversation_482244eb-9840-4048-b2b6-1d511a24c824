{"version": 3, "file": "createIconSetFromFontAwesome5.js", "sourceRoot": "", "sources": ["../src/createIconSetFromFontAwesome5.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,uBAAuB,MAAM,2BAA2B,CAAC;AAEhE,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;CACf,CAAC;AAEF,MAAM,UAAU,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,GAAG,KAAK;IAC1E,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,UAAU,GAAG,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAEzD,SAAS,cAAc,CAAC,KAAK;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3C,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAChD,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,SAAS,cAAc,CAAC,KAAK,EAAE,KAAK;QAClC,MAAM,MAAM,GAAG,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;QACpD,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QACtD,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,SAAS,sBAAsB,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,GAAG,UAAU;QACxE,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;QAElC,OAAO;YACL,UAAU,EAAE,GAAG,MAAM,IAAI,SAAS,EAAE;YACpC,QAAQ;YACR,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC;gBACzB,GAAG,EAAE;oBACH,UAAU;iBACX;gBACD,OAAO,EAAE,EAAE;aACZ,CAAC;YACF,QAAQ;SACT,CAAC;IACJ,CAAC;IAED,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC1D,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC1D,MAAM,YAAY,GAAG,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC9D,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC1D,MAAM,IAAI,GAAG,uBAAuB,CAClC;QACE,KAAK,EAAE,UAAU;QACjB,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE,YAAY;QACrB,KAAK,EAAE,UAAU;KAClB,EACD;QACE,YAAY,EAAE,SAAS;QACvB,cAAc;QACd,cAAc;KACf,CACF,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { Platform } from 'react-native';\n\nimport createMultiStyleIconSet from './createMultiStyleIconSet';\n\nexport const FA5Style = {\n  regular: 'regular',\n  light: 'light',\n  solid: 'solid',\n  brand: 'brand',\n};\n\nexport function createFA5iconSet(glyphMap, metadata = {}, fonts, pro = false) {\n  const metadataKeys = Object.keys(metadata);\n  const fontFamily = `FontAwesome5${pro ? 'Pro' : 'Free'}`;\n\n  function fallbackFamily(glyph) {\n    for (let i = 0; i < metadataKeys.length; i += 1) {\n      const family = metadataKeys[i];\n      if (metadata[family].indexOf(glyph) !== -1) {\n        return family === 'brands' ? 'brand' : family;\n      }\n    }\n\n    return 'regular';\n  }\n\n  function glyphValidator(glyph, style) {\n    const family = style === 'brand' ? 'brands' : style;\n    if (metadataKeys.indexOf(family) === -1) return false;\n    return metadata[family].indexOf(glyph) !== -1;\n  }\n\n  function createFontAwesomeStyle(styleName, fontWeight, family = fontFamily) {\n    const fontFile = fonts[styleName];\n\n    return {\n      fontFamily: `${family}-${styleName}`,\n      fontFile,\n      fontStyle: Platform.select({\n        ios: {\n          fontWeight,\n        },\n        default: {},\n      }),\n      glyphMap,\n    };\n  }\n\n  const brandIcons = createFontAwesomeStyle('Brand', '400');\n  const lightIcons = createFontAwesomeStyle('Light', '100');\n  const regularIcons = createFontAwesomeStyle('Regular', '400');\n  const solidIcons = createFontAwesomeStyle('Solid', '700');\n  const Icon = createMultiStyleIconSet(\n    {\n      brand: brandIcons,\n      light: lightIcons,\n      regular: regularIcons,\n      solid: solidIcons,\n    },\n    {\n      defaultStyle: 'regular',\n      fallbackFamily,\n      glyphValidator,\n    }\n  );\n\n  return Icon;\n}\n"]}