import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../constants';

interface IconProps {
  name: keyof typeof Ionicons.glyphMap;
  size?: number;
  color?: keyof typeof colors;
}

const Icon: React.FC<IconProps> = ({ 
  name, 
  size = 24, 
  color = 'textPrimary' 
}) => {
  return (
    <Ionicons 
      name={name} 
      size={size} 
      color={colors[color]} 
    />
  );
};

export default Icon;
