import React from 'react';
import { View, Image, StyleSheet, ViewStyle } from 'react-native';
import { colors, borderRadius } from '../../constants';
import Text from './Text';

interface AvatarProps {
  source?: { uri: string } | number;
  size?: 'small' | 'medium' | 'large';
  name?: string;
  style?: ViewStyle;
}

const Avatar: React.FC<AvatarProps> = ({ 
  source, 
  size = 'medium', 
  name, 
  style 
}) => {
  const avatarStyle = [
    styles.base,
    styles[size],
    style,
  ];

  const getInitials = (fullName: string) => {
    return fullName
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getFontSize = () => {
    switch (size) {
      case 'small': return 12;
      case 'large': return 20;
      default: return 16;
    }
  };

  if (source) {
    return (
      <Image 
        source={source} 
        style={avatarStyle}
        resizeMode="cover"
      />
    );
  }

  return (
    <View style={[avatarStyle, styles.placeholder]}>
      <Text 
        variant="button" 
        color="textOnPrimary"
        style={{ fontSize: getFontSize() }}
      >
        {name ? getInitials(name) : '?'}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: borderRadius.full,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Sizes
  small: {
    width: 32,
    height: 32,
  },
  medium: {
    width: 40,
    height: 40,
  },
  large: {
    width: 56,
    height: 56,
  },
  
  placeholder: {
    backgroundColor: colors.gray400,
  },
});

export default Avatar;
