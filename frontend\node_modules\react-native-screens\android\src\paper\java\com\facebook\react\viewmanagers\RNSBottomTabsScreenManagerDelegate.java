/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ColorPropConverter;
import com.facebook.react.bridge.DynamicFromObject;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNSBottomTabsScreenManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNSBottomTabsScreenManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNSBottomTabsScreenManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "isFocused":
        mViewManager.setIsFocused(view, value == null ? false : (boolean) value);
        break;
      case "tabKey":
        mViewManager.setTabKey(view, value == null ? null : (String) value);
        break;
      case "title":
        mViewManager.setTitle(view, value == null ? null : (String) value);
        break;
      case "badgeValue":
        mViewManager.setBadgeValue(view, value == null ? null : (String) value);
        break;
      case "orientation":
        mViewManager.setOrientation(view, (String) value);
        break;
      case "iconResourceName":
        mViewManager.setIconResourceName(view, value == null ? null : (String) value);
        break;
      case "iconResource":
        mViewManager.setIconResource(view, (ReadableMap) value);
        break;
      case "tabBarItemBadgeTextColor":
        mViewManager.setTabBarItemBadgeTextColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "tabBarItemBadgeBackgroundColor":
        mViewManager.setTabBarItemBadgeBackgroundColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "standardAppearance":
        mViewManager.setStandardAppearance(view, new DynamicFromObject(value));
        break;
      case "scrollEdgeAppearance":
        mViewManager.setScrollEdgeAppearance(view, new DynamicFromObject(value));
        break;
      case "iconType":
        mViewManager.setIconType(view, (String) value);
        break;
      case "iconImageSource":
        mViewManager.setIconImageSource(view, (ReadableMap) value);
        break;
      case "iconSfSymbolName":
        mViewManager.setIconSfSymbolName(view, value == null ? null : (String) value);
        break;
      case "selectedIconImageSource":
        mViewManager.setSelectedIconImageSource(view, (ReadableMap) value);
        break;
      case "selectedIconSfSymbolName":
        mViewManager.setSelectedIconSfSymbolName(view, value == null ? null : (String) value);
        break;
      case "systemItem":
        mViewManager.setSystemItem(view, (String) value);
        break;
      case "specialEffects":
        mViewManager.setSpecialEffects(view, (ReadableMap) value);
        break;
      case "overrideScrollViewContentInsetAdjustmentBehavior":
        mViewManager.setOverrideScrollViewContentInsetAdjustmentBehavior(view, value == null ? true : (boolean) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
