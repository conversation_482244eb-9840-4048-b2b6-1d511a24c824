{"version": 3, "names": ["React", "NavigationBuilderContext", "createContext", "onDispatchAction", "undefined", "onOptionsChange", "scheduleUpdate", "Error", "flushUpdates"], "sourceRoot": "../../src", "sources": ["NavigationBuilderContext.tsx"], "mappings": ";;AAKA,OAAO,KAAKA,KAAK,MAAM,OAAO;AA6C9B;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,gBAAGD,KAAK,CAACE,aAAa,CAaxD;EACDC,gBAAgB,EAAEA,CAAA,KAAMC,SAAS;EACjCC,eAAe,EAAEA,CAAA,KAAMD,SAAS;EAChCE,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAM,IAAIC,KAAK,CAAC,iDAAiD,CAAC;EACpE,CAAC;EACDC,YAAY,EAAEA,CAAA,KAAM;IAClB,MAAM,IAAID,KAAK,CAAC,+CAA+C,CAAC;EAClE;AACF,CAAC,CAAC", "ignoreList": []}