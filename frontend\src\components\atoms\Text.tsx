import React from 'react';
import { Text as RNText, TextProps as RNTextProps, StyleSheet } from 'react-native';
import { colors, typography } from '../../constants';

interface TextProps extends RNTextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'body' | 'bodySmall' | 'caption' | 'button';
  color?: keyof typeof colors;
  children: React.ReactNode;
}

const Text: React.FC<TextProps> = ({ 
  variant = 'body', 
  color = 'textPrimary', 
  style, 
  children, 
  ...props 
}) => {
  const textStyle = [
    styles.base,
    typography.styles[variant],
    { color: colors[color] },
    style,
  ];

  return (
    <RNText style={textStyle} {...props}>
      {children}
    </RNText>
  );
};

const styles = StyleSheet.create({
  base: {
    fontFamily: 'System', // Use system font
  },
});

export default Text;
