import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing } from '../constants';
import { Text } from '../components/atoms';

const ProfileScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text variant="h2" color="textPrimary">
          Profile
        </Text>
        <Text variant="body" color="textSecondary" style={styles.placeholder}>
          Profile screen coming soon...
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: spacing.screenPadding,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    marginTop: spacing.md,
    textAlign: 'center',
  },
});

export default ProfileScreen;
