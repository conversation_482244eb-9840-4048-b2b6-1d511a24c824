import React from 'react';
import { View, StyleSheet } from 'react-native';
import { spacing } from '../../constants';
import { Text, Avatar } from '../atoms';

interface UserInfoProps {
  name: string;
  serviceName?: string;
  avatarSource?: { uri: string };
  size?: 'small' | 'medium' | 'large';
  textColor?: keyof typeof colors;
  secondaryTextColor?: keyof typeof colors;
}

const UserInfo: React.FC<UserInfoProps> = ({
  name,
  serviceName,
  avatarSource,
  size = 'medium',
  textColor = 'textPrimary',
  secondaryTextColor = 'textSecondary'
}) => {
  return (
    <View style={styles.container}>
      <Avatar
        source={avatarSource}
        name={name}
        size={size}
      />
      <View style={styles.textContainer}>
        <Text variant="body" color={textColor}>
          {name}
        </Text>
        {serviceName && (
          <Text variant="bodySmall" color={secondaryTextColor}>
            {serviceName}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: spacing.md,
    flex: 1,
  },
});

export default UserInfo;
