import React from 'react';
import { View, StyleSheet } from 'react-native';
import { spacing } from '../../constants';
import { Text, Avatar } from '../atoms';

interface UserInfoProps {
  name: string;
  serviceName?: string;
  avatarSource?: { uri: string };
  size?: 'small' | 'medium' | 'large';
}

const UserInfo: React.FC<UserInfoProps> = ({ 
  name, 
  serviceName, 
  avatarSource, 
  size = 'medium' 
}) => {
  return (
    <View style={styles.container}>
      <Avatar 
        source={avatarSource} 
        name={name} 
        size={size}
      />
      <View style={styles.textContainer}>
        <Text variant="body" color="textPrimary">
          {name}
        </Text>
        {serviceName && (
          <Text variant="bodySmall" color="textSecondary">
            {serviceName}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: spacing.md,
    flex: 1,
  },
});

export default UserInfo;
