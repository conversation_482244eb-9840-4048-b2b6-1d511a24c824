import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors, spacing } from '../../constants';
import { Text } from '../atoms';
import { SummaryCard } from '../molecules';
import { ScheduleStats } from '../../services/types';

interface StatsOverviewProps {
  stats: ScheduleStats;
}

const StatsOverview: React.FC<StatsOverviewProps> = ({ stats }) => {
  return (
    <View style={styles.container}>
      {/* Missed Schedules - Full Width */}
      <SummaryCard 
        value={stats.missed_schedules}
        label="Missed Scheduled"
        valueColor="error"
      />
      
      {/* Two Column Layout */}
      <View style={styles.row}>
        <SummaryCard 
          value={stats.upcoming_today}
          label="Upcoming Today's Schedule"
          valueColor="accent"
        />
        
        <SummaryCard 
          value={stats.completed_today}
          label="Today's Completed Schedule"
          valueColor="success"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: spacing.elementGap,
  },
  row: {
    flexDirection: 'row',
    gap: spacing.elementGap,
  },
});

export default StatsOverview;
