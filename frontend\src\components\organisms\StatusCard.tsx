import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors, spacing, borderRadius, shadows } from '../../constants';
import { Text, Button } from '../atoms';
import { UserInfo, ScheduleItem } from '../molecules';

interface StatusCardProps {
  user: {
    name: string;
    avatarUrl?: string;
  };
  location: string;
  dateTime: string;
  timeRange: string;
  onClockOut: () => void;
  isInProgress?: boolean;
}

const StatusCard: React.FC<StatusCardProps> = ({ 
  user, 
  location, 
  dateTime, 
  timeRange, 
  onClockOut,
  isInProgress = true
}) => {
  return (
    <View style={styles.container}>
      <UserInfo 
        name={user.name}
        avatarSource={user.avatarUrl ? { uri: user.avatarUrl } : undefined}
        size="medium"
      />
      
      <View style={styles.divider} />
      
      <ScheduleItem 
        location={location}
        dateTime={dateTime}
        timeRange={timeRange}
      />
      
      <Button 
        variant="secondary" 
        onPress={onClockOut}
        fullWidth
        style={styles.button}
      >
        {isInProgress ? 'Clock-Out Now' : 'Clock-In Now'}
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    ...shadows.card,
  },
  divider: {
    height: 1,
    backgroundColor: colors.primaryLight,
    marginVertical: spacing.md,
    opacity: 0.3,
  },
  button: {
    marginTop: spacing.md,
    backgroundColor: colors.white,
  },
});

export default StatusCard;
