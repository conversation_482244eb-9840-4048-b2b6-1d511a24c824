{"version": 3, "names": ["checkDuplicateRouteNames", "state", "duplicates", "getRouteNames", "location", "routes", "for<PERSON>ach", "route", "currentLocation", "name", "routeNames", "routeName", "push"], "sourceRoot": "../../src", "sources": ["checkDuplicateRouteNames.tsx"], "mappings": ";;AAEA,OAAO,SAASA,wBAAwBA,CAACC,KAAsB,EAAE;EAC/D,MAAMC,UAAsB,GAAG,EAAE;EAEjC,MAAMC,aAAa,GAAGA,CACpBC,QAAgB,EAChBH,KAAsD,KACnD;IACHA,KAAK,CAACI,MAAM,CAACC,OAAO,CAAEC,KAA+B,IAAK;MACxD,MAAMC,eAAe,GAAGJ,QAAQ,GAC5B,GAAGA,QAAQ,MAAMG,KAAK,CAACE,IAAI,EAAE,GAC7BF,KAAK,CAACE,IAAI;MAEdF,KAAK,CAACN,KAAK,EAAES,UAAU,EAAEJ,OAAO,CAAEK,SAAS,IAAK;QAC9C,IAAIA,SAAS,KAAKJ,KAAK,CAACE,IAAI,EAAE;UAC5BP,UAAU,CAACU,IAAI,CAAC,CACdJ,eAAe,EACf,GAAGA,eAAe,MAAMD,KAAK,CAACE,IAAI,EAAE,CACrC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,IAAIF,KAAK,CAACN,KAAK,EAAE;QACfE,aAAa,CAACK,eAAe,EAAED,KAAK,CAACN,KAAK,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ,CAAC;EAEDE,aAAa,CAAC,EAAE,EAAEF,KAAK,CAAC;EAExB,OAAOC,UAAU;AACnB", "ignoreList": []}