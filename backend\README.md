# Caregiver Shift Tracker Backend API

A professional Go backend API for caregiver shift tracking and visit management system. This API allows caregivers to manage their schedules, track visits, and update task progress with Electronic Visit Verification (EVV) compliance.

## Features

- **Schedule Management**: View all schedules, today's schedules, and schedule statistics
- **Visit Tracking**: Start and end visits with geolocation capture
- **Task Management**: Update task status with completion tracking
- **EVV Compliance**: Timestamp and geolocation logging for visit verification
- **Professional Architecture**: Clean architecture with repository pattern, service layer, and proper error handling
- **Comprehensive Testing**: Unit tests for all services and handlers
- **API Documentation**: Swagger/OpenAPI documentation
- **Structured Logging**: JSON-formatted logging with different levels
- **Middleware**: CORS, rate limiting, security headers, and request logging

## Architecture

```
backend/
├── cmd/server/          # Application entry point
├── internal/
│   ├── config/         # Configuration management
│   ├── database/       # Database connection and migrations
│   ├── handlers/       # HTTP handlers (controllers)
│   ├── middleware/     # Custom middleware
│   ├── models/         # Data models and DTOs
│   ├── repositories/   # Data access layer
│   └── services/       # Business logic layer
├── docs/               # Swagger documentation
└── README.md
```

## API Endpoints

### Schedules
- `GET /api/v1/schedules` - Get all schedules with filtering
- `GET /api/v1/schedules/today?caregiver_id={id}` - Get today's schedules
- `GET /api/v1/schedules/stats?caregiver_id={id}` - Get schedule statistics
- `GET /api/v1/schedules/{id}` - Get schedule details with tasks and visit info
- `POST /api/v1/schedules/{id}/start` - Start a visit with geolocation
- `POST /api/v1/schedules/{id}/end` - End a visit with geolocation

### Tasks
- `GET /api/v1/tasks/{id}` - Get task details
- `PUT /api/v1/tasks/{id}` - Update task status (completed/not_completed)

### Visits
- `GET /api/v1/visits/schedule/{scheduleId}` - Get visit details for a schedule

### Documentation
- `GET /swagger/index.html` - Swagger UI documentation
- `GET /health` - Health check endpoint

## Data Models

### Schedule
- Client information and caregiver assignment
- Start/end times and location
- Status tracking (scheduled, in_progress, completed, missed)
- Associated visit and tasks

### Visit
- Start/end timestamps with geolocation
- Status tracking (not_started, in_progress, completed)
- Notes and verification data

### Task
- Care activities to be completed during visits
- Status tracking (pending, completed, not_completed)
- Reason tracking for incomplete tasks

## Getting Started

### Prerequisites
- Go 1.21 or higher
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies:
```bash
go mod tidy
```

3. Build the application:
```bash
go build -o caregiver-shift-tracker.exe ./cmd/server
```

4. Run the application:
```bash
./caregiver-shift-tracker.exe
```

The server will start on port 8080 by default.

### Configuration

The application uses environment variables for configuration:

- `ENVIRONMENT` - Application environment (development/production)
- `PORT` - Server port (default: 8080)
- `DATABASE_URL` - SQLite database file path (default: caregiver_shift_tracker.db)
- `LOG_LEVEL` - Logging level (default: info)

### Testing

Run all tests:
```bash
go test ./... -v
```

Run specific test suites:
```bash
go test ./internal/services/... -v
go test ./internal/handlers/... -v
```

## Sample Data

The application includes sample data for testing:

- **Locations**: 3 sample client locations in Springfield, IL
- **Schedules**: 4 sample schedules with different statuses
- **Tasks**: 7 sample care tasks (medication, vital signs, bathing, etc.)
- **Visits**: Corresponding visit records for each schedule

## API Usage Examples

### Get Today's Schedules
```bash
curl "http://localhost:8080/api/v1/schedules/today?caregiver_id=1"
```

### Start a Visit
```bash
curl -X POST "http://localhost:8080/api/v1/schedules/1/start" \
  -H "Content-Type: application/json" \
  -d '{"latitude": 40.7128, "longitude": -74.0060}'
```

### Update Task Status
```bash
curl -X PUT "http://localhost:8080/api/v1/tasks/1" \
  -H "Content-Type: application/json" \
  -d '{"status": "completed"}'
```

### Mark Task as Not Completed
```bash
curl -X PUT "http://localhost:8080/api/v1/tasks/2" \
  -H "Content-Type: application/json" \
  -d '{"status": "not_completed", "reason": "Client refused medication"}'
```

## Development

### Project Structure
- **Clean Architecture**: Separation of concerns with clear layers
- **Repository Pattern**: Abstracted data access layer
- **Service Layer**: Business logic and validation
- **Handler Layer**: HTTP request/response handling
- **Middleware**: Cross-cutting concerns

### Code Quality
- Comprehensive unit tests with mocking
- Structured logging with contextual information
- Error handling with proper HTTP status codes
- Input validation and sanitization
- Security headers and CORS configuration

### Database
- SQLite database for simplicity and portability
- Automatic migrations on startup
- Sample data seeding for development

## Production Considerations

- Use environment variables for sensitive configuration
- Implement proper authentication and authorization
- Use a production database (PostgreSQL, MySQL)
- Add monitoring and metrics collection
- Implement proper logging aggregation
- Add rate limiting and request throttling
- Use HTTPS in production

## License

This project is licensed under the MIT License.
