# Caregiver Shift Tracker - Frontend

A React Native + Expo mobile application for caregiver shift tracking and visit management. This app provides a clean, modern interface for caregivers to manage their schedules, track visits, and update task progress.

## Features

- **Schedule Management**: View today's schedules and upcoming visits
- **Visit Tracking**: Clock in/out with location tracking
- **Statistics Overview**: Real-time stats for missed, upcoming, and completed schedules
- **Modern UI**: Clean design following atomic design principles
- **Cross-Platform**: Works on iOS, Android, and Web
- **Offline Support**: Mock data for development and testing

## Technology Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Navigation**: React Navigation (Bottom Tabs)
- **State Management**: React Query for server state
- **UI Components**: Custom atomic design system
- **Icons**: Expo Vector Icons (Ionicons)

## Project Structure

```
frontend/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── atoms/          # Basic components (Button, Text, etc.)
│   │   ├── molecules/      # Composed components (SummaryCard, UserInfo)
│   │   └── organisms/      # Complex components (StatusCard, ScheduleList)
│   ├── screens/            # Screen components
│   ├── navigation/         # Navigation configuration
│   ├── services/           # API services and types
│   ├── hooks/              # Custom React hooks
│   ├── constants/          # Design system constants
│   └── providers/          # Context providers
├── assets/                 # Static assets
└── App.tsx                # Main app component
```

## Design System

### Colors
- **Primary**: Dark teal (#0F766E) for status cards and primary actions
- **Accent**: Orange (#F97316) for highlights and numbers
- **Success**: Green (#10B981) for completed items
- **Error**: Red (#EF4444) for missed/cancelled items

### Typography
- System font with consistent sizing scale
- Font weights: normal (400), medium (500), semibold (600), bold (700)

### Spacing
- 8px grid system for consistent spacing
- Screen padding: 20px
- Card padding: 16px
- Element gaps: 12px

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Expo CLI (optional, but recommended)

### Installation

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open the app:
   - **Web**: Press `w` or visit http://localhost:8081
   - **iOS**: Press `i` or scan QR code with Camera app
   - **Android**: Press `a` or scan QR code with Expo Go app

## Configuration

### API Integration
The app is configured to use mock data by default. To connect to the real backend:

1. Update `frontend/src/services/api.ts`:
```typescript
const USE_MOCK_DATA = false; // Change to false
const API_BASE_URL = 'http://your-backend-url:8080/api/v1';
```

2. Ensure your backend is running and accessible

### Mock Data
Mock data is provided in `src/services/mockData.ts` for development and testing. It includes:
- Sample schedules with different statuses
- Statistics data
- Simulated API delays

## Components

### Atomic Components
- **Text**: Typography component with variant support
- **Button**: Customizable button with multiple variants
- **Avatar**: User avatar with fallback to initials
- **Badge**: Status badges for schedules
- **Icon**: Ionicons wrapper

### Molecular Components
- **SummaryCard**: Statistics display card
- **UserInfo**: User information with avatar
- **ScheduleItem**: Schedule details display

### Organism Components
- **StatusCard**: Current visit status card
- **StatsOverview**: Statistics overview section
- **ScheduleCard**: Individual schedule card
- **ScheduleList**: List of schedule cards

## Screens

### HomeScreen
Main dashboard showing:
- Welcome message
- Current visit status (if any)
- Statistics overview
- Today's schedule list

### ProfileScreen
User profile screen (placeholder for future implementation)

## API Integration

The app uses React Query for efficient data fetching and caching:

- **Queries**: Automatic background refetching and caching
- **Mutations**: Optimistic updates for clock in/out actions
- **Error Handling**: Graceful error handling with user feedback

### Available Hooks
- `useTodaySchedules()`: Fetch today's schedules
- `useScheduleStats()`: Fetch schedule statistics
- `useStartVisit()`: Start a visit (clock in)
- `useEndVisit()`: End a visit (clock out)

## Development

### Running Tests
```bash
npm test
```

### Building for Production
```bash
# Web
npm run build:web

# iOS
expo build:ios

# Android
expo build:android
```

### Code Quality
- TypeScript for type safety
- ESLint for code linting
- Prettier for code formatting

## Deployment

### Web Deployment
The app can be deployed as a web application:
```bash
npm run build:web
```

### Mobile App Stores
Use Expo's build service or eject to build native apps for app stores.

## Contributing

1. Follow the atomic design pattern for new components
2. Use TypeScript for all new code
3. Follow the established naming conventions
4. Test components on multiple screen sizes
5. Update documentation for new features

## Troubleshooting

### Common Issues

1. **Metro bundler issues**: Clear cache with `expo start -c`
2. **Package version conflicts**: Run `expo install --fix`
3. **Network issues**: Check API_BASE_URL configuration
4. **Build errors**: Ensure all dependencies are properly installed

### Performance Tips

1. Use React.memo for expensive components
2. Implement proper key props for lists
3. Optimize images and assets
4. Use lazy loading for large lists

## Future Enhancements

- [ ] Push notifications for schedule reminders
- [ ] Offline data synchronization
- [ ] GPS location tracking
- [ ] Photo capture for visit verification
- [ ] Task management interface
- [ ] Report generation
- [ ] Multi-language support
