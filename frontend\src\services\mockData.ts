import { Schedule, ScheduleStats } from './types';

export const mockSchedules: Schedule[] = [
  {
    id: 1,
    client_name: '<PERSON><PERSON>',
    client_address: 'Casa Grande Apartment',
    caregiver_name: '<PERSON>',
    start_time: '2025-01-15T09:00:00Z',
    end_time: '2025-01-15T10:00:00Z',
    status: 'scheduled',
  },
  {
    id: 2,
    client_name: '<PERSON><PERSON>',
    client_address: 'Casa Grande Apartment',
    caregiver_name: '<PERSON>',
    start_time: '2025-01-15T09:00:00Z',
    end_time: '2025-01-15T10:00:00Z',
    status: 'in_progress',
  },
  {
    id: 3,
    client_name: '<PERSON><PERSON>',
    client_address: 'Casa Grande Apartment',
    caregiver_name: '<PERSON>',
    start_time: '2025-01-15T09:00:00Z',
    end_time: '2025-01-15T10:00:00Z',
    status: 'completed',
  },
  {
    id: 4,
    client_name: '<PERSON><PERSON>',
    client_address: 'Casa Grande Apartment',
    caregiver_name: '<PERSON>',
    start_time: '2025-01-15T09:00:00Z',
    end_time: '2025-01-15T10:00:00Z',
    status: 'cancelled',
  },
];

export const mockStats: ScheduleStats = {
  total_schedules: 15,
  today_schedules: 4,
  completed_today: 5,
  missed_schedules: 7,
  upcoming_today: 12,
};

// Mock API functions for development
export const mockApi = {
  getSchedules: async (): Promise<Schedule[]> => {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
    return mockSchedules;
  },

  getTodaySchedules: async (): Promise<Schedule[]> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    return mockSchedules;
  },

  getScheduleStats: async (): Promise<ScheduleStats> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    return mockStats;
  },

  getScheduleById: async (id: number): Promise<Schedule> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const schedule = mockSchedules.find(s => s.id === id);
    if (!schedule) {
      throw new Error('Schedule not found');
    }
    return schedule;
  },
};
