/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.Dynamic;
import com.facebook.react.bridge.ReadableMap;


public interface RNSBottomTabsScreenManagerInterface<T extends View>  {
  void setIsFocused(T view, boolean value);
  void setTabKey(T view, @Nullable String value);
  void setTitle(T view, @Nullable String value);
  void setBadgeValue(T view, @Nullable String value);
  void setOrientation(T view, @Nullable String value);
  void setIconResourceName(T view, @Nullable String value);
  void setIconResource(T view, @Nullable ReadableMap value);
  void setTabBarItemBadgeTextColor(T view, @Nullable Integer value);
  void setTabBarItemBadgeBackgroundColor(T view, @Nullable Integer value);
  void setStandardAppearance(T view, Dynamic value);
  void setScrollEdgeAppearance(T view, Dynamic value);
  void setIconType(T view, @Nullable String value);
  void setIconImageSource(T view, @Nullable ReadableMap value);
  void setIconSfSymbolName(T view, @Nullable String value);
  void setSelectedIconImageSource(T view, @Nullable ReadableMap value);
  void setSelectedIconSfSymbolName(T view, @Nullable String value);
  void setSystemItem(T view, @Nullable String value);
  void setSpecialEffects(T view, @Nullable ReadableMap value);
  void setOverrideScrollViewContentInsetAdjustmentBehavior(T view, boolean value);
}
