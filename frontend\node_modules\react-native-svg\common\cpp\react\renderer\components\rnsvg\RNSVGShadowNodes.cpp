#include <react/renderer/components/rnsvg/ShadowNodes.h>

namespace facebook::react {

extern const char RNSVGCircleComponentName[] = "RNSVGCircle";
extern const char RNSVGClipPathComponentName[] = "RNSVGClipPath";
extern const char RNSVGDefsComponentName[] = "RNSVGDefs";
extern const char RNSVGEllipseComponentName[] = "RNSVGEllipse";
extern const char RNSVGFeBlendComponentName[] = "RNSVGFeBlend";
extern const char RNSVGFeColorMatrixComponentName[] = "RNSVGFeColorMatrix";
extern const char RNSVGFeCompositeComponentName[] = "RNSVGFeComposite";
extern const char RNSVGFeFloodComponentName[] = "RNSVGFeFlood";
extern const char RNSVGFeGaussianBlurComponentName[] = "RNSVGFeGaussianBlur";
extern const char RNSVGFeMergeComponentName[] = "RNSVGFeMerge";
extern const char RNSVGFeOffsetComponentName[] = "RNSVGFeOffset";
extern const char RNSVGFilterComponentName[] = "RNSVGFilter";
extern const char RNSVGForeignObjectComponentName[] = "RNSVGForeignObject";
extern const char RNSVGGroupComponentName[] = "RNSVGGroup";
extern const char RNSVGLinearGradientComponentName[] = "RNSVGLinearGradient";
extern const char RNSVGLineComponentName[] = "RNSVGLine";
extern const char RNSVGMarkerComponentName[] = "RNSVGMarker";
extern const char RNSVGMaskComponentName[] = "RNSVGMask";
extern const char RNSVGPathComponentName[] = "RNSVGPath";
extern const char RNSVGPatternComponentName[] = "RNSVGPattern";
extern const char RNSVGRadialGradientComponentName[] = "RNSVGRadialGradient";
extern const char RNSVGRectComponentName[] = "RNSVGRect";
extern const char RNSVGSymbolComponentName[] = "RNSVGSymbol";
extern const char RNSVGTextComponentName[] = "RNSVGText";
extern const char RNSVGTextPathComponentName[] = "RNSVGTextPath";
extern const char RNSVGTSpanComponentName[] = "RNSVGTSpan";
extern const char RNSVGUseComponentName[] = "RNSVGUse";

} // namespace facebook::react
